@extends('layouts.admin')

@section('page-title')
    {{ __('معاينة فواتير البيع') }}
@endsection

@push('css-page')
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<style>
    .preview-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .preview-title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 10px;
        text-align: center;
    }
    
    .preview-subtitle {
        font-size: 14px;
        opacity: 0.9;
        text-align: center;
    }
    
    .filters-summary {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .filters-title {
        font-weight: 600;
        color: #495057;
        margin-bottom: 10px;
        font-size: 16px;
    }
    
    .filter-item {
        background: #e9ecef;
        padding: 5px 10px;
        border-radius: 15px;
        display: inline-block;
        margin: 3px;
        font-size: 12px;
        color: #495057;
    }
    
    .statistics-cards {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .stat-card {
        flex: 1;
        min-width: 200px;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .stat-number {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 12px;
        color: #6c757d;
        text-transform: uppercase;
    }
    
    .stat-total .stat-number { color: #007bff; }
    .stat-completed .stat-number { color: #28a745; }
    .stat-delivery .stat-number { color: #ffc107; }
    .stat-returned .stat-number { color: #dc3545; }
    .stat-cancelled .stat-number { color: #6c757d; }
    .stat-amount .stat-number { color: #17a2b8; }
    
    .preview-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .table-header {
        background: #343a40;
        color: white;
        padding: 15px;
        font-weight: 600;
    }
    
    .table th {
        background: #495057;
        color: white;
        font-weight: 600;
        font-size: 11px;
        border: none;
        padding: 12px 8px;
    }
    
    .table td {
        padding: 10px 8px;
        font-size: 12px;
        border-bottom: 1px solid #dee2e6;
        vertical-align: middle;
    }
    
    .table tbody tr:hover {
        background-color: #f8f9fa;
    }
    
    .badge {
        font-size: 10px;
        padding: 4px 8px;
    }
    
    .action-buttons {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
    }

    .action-buttons .btn {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
    }

    .action-buttons .btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(0,0,0,0.3);
    }

    .action-buttons .btn i {
        font-size: 18px;
    }
    
    @media print {
        .print-button, .no-print {
            display: none !important;
        }
        
        .preview-header {
            background: #667eea !important;
            -webkit-print-color-adjust: exact;
        }
        
        .table th {
            background: #495057 !important;
            -webkit-print-color-adjust: exact;
        }
    }
    
    @media (max-width: 768px) {
        .statistics-cards {
            flex-direction: column;
        }
        
        .stat-card {
            min-width: auto;
        }
        
        .table-responsive {
            font-size: 10px;
        }
    }
</style>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('الرئيسية') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('invoice.processing.invoice.processor') }}">{{ __('معالج فواتير البيع') }}</a></li>
    <li class="breadcrumb-item active">{{ __('معاينة البيانات') }}</li>
@endsection

@section('content')
    <div class="container-fluid">
        <!-- رأس المعاينة -->
        <div class="preview-header">
            <div class="preview-title">
                <i class="ti ti-eye me-2"></i>{{ __('معاينة فواتير البيع') }}
            </div>
            <div class="preview-subtitle">
                <i class="ti ti-calendar me-1"></i>{{ __('تاريخ المعاينة') }}: {{ now()->format('Y-m-d H:i:s') }}
                <span class="ms-3">
                    <i class="ti ti-user me-1"></i>{{ __('المستخدم') }}: {{ Auth::user()->name }}
                </span>
            </div>
        </div>

        <!-- ملخص الفلاتر المطبقة -->
        @if(!empty($appliedFilters))
        <div class="filters-summary">
            <div class="filters-title">
                <i class="ti ti-filter me-2"></i>{{ __('الفلاتر المطبقة') }}:
            </div>
            <div>
                @foreach($appliedFilters as $filter)
                    <span class="filter-item">{{ $filter }}</span>
                @endforeach
            </div>
        </div>
        @endif

        <!-- بطاقات الإحصائيات -->
        <div class="statistics-cards">
            <div class="stat-card stat-total">
                <div class="stat-number">{{ $statistics['total'] }}</div>
                <div class="stat-label">{{ __('إجمالي الفواتير') }}</div>
            </div>
            
            <div class="stat-card stat-completed">
                <div class="stat-number">{{ $statistics['completed'] }}</div>
                <div class="stat-label">{{ __('مكتملة') }}</div>
            </div>
            
            @if($statistics['delivery_in_progress'] > 0)
            <div class="stat-card stat-delivery">
                <div class="stat-number">{{ $statistics['delivery_in_progress'] }}</div>
                <div class="stat-label">{{ __('جاري التوصيل') }}</div>
            </div>
            @endif
            
            @if($statistics['returned'] > 0)
            <div class="stat-card stat-returned">
                <div class="stat-number">{{ $statistics['returned'] }}</div>
                <div class="stat-label">{{ __('مرتجعة') }}</div>
            </div>
            @endif
            
            @if($statistics['cancelled'] > 0)
            <div class="stat-card stat-cancelled">
                <div class="stat-number">{{ $statistics['cancelled'] }}</div>
                <div class="stat-label">{{ __('ملغية') }}</div>
            </div>
            @endif
            
            <div class="stat-card stat-amount">
                <div class="stat-number">{{ number_format($statistics['total_amount'], 2) }}</div>
                <div class="stat-label">{{ __('إجمالي المبلغ (ريال)') }}</div>
            </div>
        </div>

        <!-- جدول البيانات -->
        <div class="preview-table">
            <div class="table-header">
                <i class="ti ti-table me-2"></i>{{ __('تفاصيل الفواتير') }} ({{ count($posPayments) }} {{ __('فاتورة') }})
            </div>
            
            <div class="table-responsive">
                <table class="table table-striped mb-0">
                    <thead>
                        <tr>
                            <th>{{ __('رقم الفاتورة') }}</th>
                            <th>{{ __('التاريخ') }}</th>
                            <th>{{ __('العميل') }}</th>
                            <th>{{ __('المستودع') }}</th>
                            <th>{{ __('المجموع الفرعي') }}</th>
                            <th>{{ __('الخصم') }}</th>
                            <th>{{ __('المجموع') }}</th>
                            <th>{{ __('طريقة الدفع') }}</th>
                            <th>{{ __('منشئ الفاتورة') }}</th>
                            <th>{{ __('المستخدم') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($posPayments as $pos)
                            <tr>
                                <td class="fw-bold">{{ AUth::user()->posNumberFormat($pos->id) }}</td>
                                <td>{{ Auth::user()->dateFormat($pos->pos_date) }}</td>
                                <td>{{ !empty($pos->customer) ? $pos->customer->name : __('عميل نقدي') }}</td>
                                <td>{{ !empty($pos->warehouse) ? $pos->warehouse->name : '-' }}</td>
                                <td>{{ Auth::user()->priceFormat($pos->getSubTotal()) }}</td>
                                <td>{{ Auth::user()->priceFormat($pos->getTotalDiscount()) }}</td>
                                <td class="fw-bold">{{ Auth::user()->priceFormat($pos->getTotal()) }}</td>
                                <td>
                                    @if($pos->status_type == 'returned')
                                        <span class="badge bg-danger">{{ __('مرتجع بضاعة') }}</span>
                                    @elseif($pos->status_type == 'cancelled')
                                        <span class="badge bg-secondary">{{ __('ملغية') }}</span>
                                    @elseif(!empty($pos->customer) && $pos->customer->is_delivery)
                                        @if($pos->is_payment_set)
                                            <span class="badge bg-success">{{ __('تم التحصيل') }}</span>
                                        @else
                                            <span class="badge bg-warning text-dark">{{ __('جاري التحصيل') }}</span>
                                        @endif
                                    @elseif(isset($pos->posPayment) && $pos->posPayment->payment_type)
                                        @if($pos->posPayment->payment_type == 'cash')
                                            <span class="badge bg-success">{{ __('نقد') }}</span>
                                        @elseif($pos->posPayment->payment_type == 'network')
                                            <span class="badge bg-info">{{ __('شبكة') }}</span>
                                        @elseif($pos->posPayment->payment_type == 'split')
                                            <span class="badge bg-warning text-dark">{{ __('دفع مقسم') }}</span>
                                        @else
                                            <span class="badge bg-danger">{{ __('غير مدفوع') }}</span>
                                        @endif
                                    @else
                                        <span class="badge bg-danger">{{ __('غير مدفوع') }}</span>
                                    @endif
                                </td>
                                <td>
                                    @if(!empty($pos->createdBy))
                                        @if($pos->createdBy->type == 'company')
                                            @if(!empty($pos->user_id) && !empty(\App\Models\User::find($pos->user_id)))
                                                {{ \App\Models\User::find($pos->user_id)->name }}
                                            @elseif(!empty($pos->shift) && !empty($pos->shift->creator))
                                                {{ $pos->shift->creator->name }}
                                            @else
                                                {{ $pos->createdBy->name }}
                                            @endif
                                        @else
                                            {{ $pos->createdBy->name }}
                                        @endif
                                    @else
                                        {{ __('غير معروف') }}
                                    @endif
                                </td>
                                <td>
                                    @if(!empty($pos->user))
                                        {{ $pos->user->name }}
                                    @elseif(!empty($pos->user_id) && !empty(\App\Models\User::find($pos->user_id)))
                                        {{ \App\Models\User::find($pos->user_id)->name }}
                                    @elseif(!empty($pos->shift) && !empty($pos->shift->creator))
                                        {{ $pos->shift->creator->name }}
                                    @else
                                        {{ __('غير معروف') }}
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="ti ti-inbox text-muted" style="font-size: 48px;"></i>
                                    <div class="mt-2 text-muted">{{ __('لا توجد فواتير متاحة بناءً على الفلاتر المحددة') }}</div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="action-buttons no-print" style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
            <div class="btn-group-vertical" role="group">
                <button type="button" class="btn btn-primary mb-2" onclick="window.print()" title="{{ __('طباعة') }}">
                    <i class="ti ti-printer"></i>
                </button>
                <button type="button" class="btn btn-success mb-2" onclick="exportToExcel()" title="{{ __('تصدير إلى Excel') }}">
                    <i class="ti ti-file-spreadsheet"></i>
                </button>
                <button type="button" class="btn btn-info mb-2" onclick="window.location.reload()" title="{{ __('تحديث') }}">
                    <i class="ti ti-refresh"></i>
                </button>
                <button type="button" class="btn btn-secondary" onclick="window.close()" title="{{ __('إغلاق') }}">
                    <i class="ti ti-x"></i>
                </button>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<!-- مكتبة XLSX للتصدير إلى Excel -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<script>
$(document).ready(function() {
    // إضافة تأثيرات بصرية
    $('.stat-card').hide().each(function(index) {
        $(this).delay(index * 100).fadeIn(500);
    });
    
    $('.preview-table').hide().delay(600).fadeIn(800);
});

// دالة الطباعة المحسنة
function printPreview() {
    window.print();
}

// إضافة اختصار لوحة المفاتيح للطباعة
$(document).keydown(function(e) {
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        printPreview();
    }
});

// دالة تصدير إلى Excel
function exportToExcel() {
    try {
        // إنشاء جدول مؤقت للتصدير
        let table = document.querySelector('.preview-table table').cloneNode(true);

        // إزالة الأعمدة غير المرغوب فيها (إذا وجدت)
        // يمكن تخصيص هذا حسب الحاجة

        // إنشاء workbook
        let wb = XLSX.utils.table_to_book(table, {sheet: "فواتير البيع"});

        // إنشاء اسم الملف
        let fileName = 'معاينة_فواتير_البيع_' + new Date().toISOString().slice(0, 10) + '.xlsx';

        // تحميل الملف
        XLSX.writeFile(wb, fileName);

        // إظهار رسالة نجاح
        showAlert('تم تصدير البيانات بنجاح!', 'success');

    } catch (error) {
        console.error('خطأ في التصدير:', error);

        // طريقة بديلة: تصدير كـ CSV
        exportToCSV();
    }
}

// دالة تصدير بديلة إلى CSV
function exportToCSV() {
    try {
        let csv = [];
        let rows = document.querySelectorAll('.preview-table table tr');

        for (let i = 0; i < rows.length; i++) {
            let row = [], cols = rows[i].querySelectorAll('td, th');

            for (let j = 0; j < cols.length; j++) {
                let cellText = cols[j].innerText.replace(/"/g, '""');
                row.push('"' + cellText + '"');
            }

            csv.push(row.join(','));
        }

        // إنشاء الملف وتحميله
        let csvFile = new Blob([csv.join('\n')], {type: 'text/csv;charset=utf-8;'});
        let downloadLink = document.createElement('a');
        let fileName = 'معاينة_فواتير_البيع_' + new Date().toISOString().slice(0, 10) + '.csv';

        downloadLink.download = fileName;
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = 'none';

        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        showAlert('تم تصدير البيانات كملف CSV!', 'success');

    } catch (error) {
        console.error('خطأ في تصدير CSV:', error);
        showAlert('حدث خطأ أثناء التصدير', 'danger');
    }
}

// دالة إظهار التنبيهات
function showAlert(message, type) {
    // إنشاء تنبيه بسيط
    let alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً بعد 3 ثوان
    setTimeout(function() {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}
</script>
@endpush
