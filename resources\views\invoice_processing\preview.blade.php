@extends('layouts.admin')

@section('page-title')
    {{ __('Sales Invoice Processor') }}
@endsection

@push('css-page')
<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

<style>
    /* تنسيق بسيط للصفحة */
    .card-header h5 {
        color: #495057;
        font-weight: 600;
    }

    .table-border-style {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        overflow: hidden;
    }

    .table th {
        background: #f8f9fa;
        color: #495057;
        font-weight: 600;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        vertical-align: middle;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .badge {
        font-size: 0.75rem;
    }

    .btn-outline-primary {
        font-size: 0.875rem;
        padding: 0.25rem 0.5rem;
    }

    .action-btn {
        display: inline-block;
        margin: 0 2px;
    }

    .action-btn .btn {
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('الرئيسية') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('invoice.processing.invoice.processor') }}">{{ __('معالج فواتير البيع') }}</a></li>
    <li class="breadcrumb-item active">{{ __('معاينة البيانات') }}</li>
@endsection

@section('content')
    <div class="container-fluid">
        <!-- عنوان الصفحة -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">{{ __('Sales Invoice Processor') }}</h5>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول البيانات -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                <tr>
                                    <th>{{__('رقم الفاتورة')}}</th>
                                    <th>{{ __('تاريخ') }}</th>
                                    <th>{{ __('عميل') }}</th>
                                    <th>{{ __('مستودع') }}</th>
                                    <th>{{ __('المجموع الفرعي') }}</th>
                                    <th>{{ __('الخصم') }}</th>
                                    <th>{{ __('المجموع') }}</th>
                                    <th>{{ __('طريقة الدفع') }}</th>
                                    <th>{{ __('منشئ الفاتورة') }}</th>
                                    <th>{{ __('المستخدم') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                                </thead>

                                <tbody>
                                @forelse ($posPayments as $pos)
                                    <tr>
                                        <td class="Id">
                                            <a href="{{ route('invoice.processing.show', $pos->id) }}" class="btn btn-outline-primary">
                                                {{ AUth::user()->posNumberFormat($pos->id) }}
                                            </a>
                                        </td>

                                        <td>{{ Auth::user()->dateFormat($pos->pos_date) }}</td>
                                        <td>{{ !empty($pos->customer) ? $pos->customer->name : __('Walk-in Customer') }}</td>
                                        <td>{{ !empty($pos->warehouse) ? $pos->warehouse->name : '' }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getSubTotal()) }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getTotalDiscount()) }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getTotal()) }}</td>
                                        <td>
                                            @if($pos->status_type == 'returned')
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('مرتجع بضاعة') }} ↩️</span>
                                            @elseif($pos->status_type == 'cancelled')
                                                <span class="badge bg-secondary text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('ملغية') }} ❌</span>
                                            @elseif(!empty($pos->customer) && $pos->customer->is_delivery)
                                                @if($pos->is_payment_set)
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('تم التحصيل من مندوب التوصيل') }} ✅</span>
                                                @else
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('جاري التحصيل') }} 🚚</span>
                                                @endif
                                            @elseif(isset($pos->posPayment) && $pos->posPayment->payment_type)
                                                @if($pos->posPayment->payment_type == 'cash')
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('نقد') }} 💵</span>
                                                @elseif($pos->posPayment->payment_type == 'network')
                                                    <span class="badge bg-info text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('شبكة') }} 💳</span>
                                                @elseif($pos->posPayment->payment_type == 'split')
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('دفع مقسم') }} 💳 💵</span>
                                                @else
                                                    <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('غير مدفوع') }}</span>
                                                @endif
                                            @else
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('غير مدفوع') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if(!empty($pos->createdBy))
                                                @if($pos->createdBy->type == 'company')
                                                    @if(!empty($pos->user_id) && !empty(\App\Models\User::find($pos->user_id)))
                                                        {{ \App\Models\User::find($pos->user_id)->name }}
                                                    @elseif(!empty($pos->shift) && !empty($pos->shift->creator))
                                                        {{ $pos->shift->creator->name }}
                                                    @else
                                                        {{ $pos->createdBy->name }}
                                                    @endif
                                                @else
                                                    {{ $pos->createdBy->name }}
                                                @endif
                                            @else
                                                {{ __('غير معروف') }}
                                            @endif
                                        </td>
                                        <td>
                                            @if(!empty($pos->user))
                                                {{ $pos->user->name }}
                                            @elseif(!empty($pos->user_id) && !empty(\App\Models\User::find($pos->user_id)))
                                                {{ \App\Models\User::find($pos->user_id)->name }}
                                            @elseif(!empty($pos->shift) && !empty($pos->shift->creator))
                                                {{ $pos->shift->creator->name }}
                                            @else
                                                {{ __('غير معروف') }}
                                            @endif
                                        </td>
                                        <td class="Action">
                                            <div class="action-btn bg-primary ms-2">
                                                <a href="{{ route('invoice.processing.edit.products', $pos->id) }}" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="{{ __('تعديل المنتجات') }}">
                                                    <i class="ti ti-edit text-white"></i>
                                                </a>
                                            </div>
                                            <div class="action-btn bg-danger ms-2">
                                                <a href="{{ route('invoice.processing.return', $pos->id) }}" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="{{ __('مرتجع') }}" onclick="return confirm('{{ __('هل أنت متأكد من تغيير حالة الفاتورة إلى مرتجع بضاعة؟') }}')">
                                                    <i class="ti ti-arrow-back-up text-white"></i>
                                                </a>
                                            </div>
                                            <div class="action-btn bg-warning ms-2">
                                                <a href="{{ route('invoice.processing.cancel', $pos->id) }}" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="{{ __('كنسل') }}" onclick="return confirm('{{ __('هل أنت متأكد من تغيير حالة الفاتورة إلى ملغية؟') }}')">
                                                    <i class="ti ti-x text-white"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="11" class="text-center">{{ __('لا توجد فواتير متاحة') }}</td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
@endsection

@push('script-page')
<!-- jQuery (إذا لم يكن موجوداً) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<script>
$(document).ready(function() {
    // تهيئة DataTable للجدول
    $('.datatable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
        },
        "pageLength": 25,
        "order": [[ 0, "desc" ]],
        "columnDefs": [
            { "orderable": false, "targets": -1 } // تعطيل الترتيب لعمود الإجراءات
        ]
    });
});
</script>
@endpush
