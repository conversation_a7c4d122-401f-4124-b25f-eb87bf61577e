<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير تسويات مبيعات نقاط البيع</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            direction: rtl;
            text-align: right;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .header .report-info {
            font-size: 14px;
            color: #666;
        }
        
        .filters-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        
        .filters-title {
            font-weight: 600;
            font-size: 14px;
            color: #495057;
            margin-bottom: 10px;
        }
        
        .filter-item {
            margin-bottom: 5px;
            padding-right: 15px;
        }
        
        .statistics-section {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            padding: 15px;
            border-radius: 8px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
        }
        
        .stat-number {
            font-size: 18px;
            font-weight: 700;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
        }
        
        .table-container {
            margin-bottom: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10px;
        }
        
        th, td {
            border: 1px solid #dee2e6;
            padding: 8px 6px;
            text-align: center;
            vertical-align: middle;
        }
        
        th {
            background: linear-gradient(135deg, #343a40 0%, #495057 100%);
            color: white;
            font-weight: 600;
            font-size: 11px;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        tr:hover {
            background-color: #e9ecef;
        }
        
        .amount {
            font-weight: 600;
            color: #28a745;
        }
        
        .status-normal {
            color: #28a745;
            font-weight: 600;
        }
        
        .status-returned {
            color: #dc3545;
            font-weight: 600;
        }
        
        .status-cancelled {
            color: #6c757d;
            font-weight: 600;
        }
        
        .status-delivery {
            color: #ffc107;
            font-weight: 600;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                font-size: 10px;
            }
            
            .header h1 {
                font-size: 20px;
            }
            
            .statistics-section {
                background: #f8f9fa !important;
            }
        }
    </style>
</head>
<body>
    <!-- رأس التقرير -->
    <div class="header">
        <h1>تقرير تسويات مبيعات نقاط البيع</h1>
        <div class="report-info">
            <div>تاريخ إنشاء التقرير: {{ \Carbon\Carbon::parse($reportDate)->format('Y-m-d H:i:s') }}</div>
            <div>المستخدم: {{ Auth::user()->name }}</div>
            @if(request('date_from') || request('date_to'))
                <div style="margin-top: 5px; font-weight: 600; color: #007bff;">
                    فترة التقرير:
                    @if(request('date_from') && request('date_to'))
                        من {{ request('date_from') }} إلى {{ request('date_to') }}
                    @elseif(request('date_from'))
                        من {{ request('date_from') }}
                    @elseif(request('date_to'))
                        حتى {{ request('date_to') }}
                    @endif
                </div>
            @endif
        </div>
    </div>

    <!-- قسم الفلاتر المطبقة -->
    @if(!empty($appliedFilters))
    <div class="filters-section">
        <div class="filters-title">الفلاتر المطبقة:</div>
        @foreach($appliedFilters as $filter)
            <div class="filter-item">• {{ $filter }}</div>
        @endforeach
    </div>
    @endif

    <!-- قسم الإحصائيات -->
    <div class="statistics-section">
        <div class="stat-item">
            <div class="stat-number">{{ $statistics['total'] }}</div>
            <div class="stat-label">إجمالي الفواتير</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ $statistics['completed'] }}</div>
            <div class="stat-label">مكتملة</div>
        </div>
        @if($statistics['delivery_in_progress'] > 0)
        <div class="stat-item">
            <div class="stat-number">{{ $statistics['delivery_in_progress'] }}</div>
            <div class="stat-label">جاري التوصيل</div>
        </div>
        @endif
        @if($statistics['returned'] > 0)
        <div class="stat-item">
            <div class="stat-number">{{ $statistics['returned'] }}</div>
            <div class="stat-label">مرتجعة</div>
        </div>
        @endif
        @if($statistics['cancelled'] > 0)
        <div class="stat-item">
            <div class="stat-number">{{ $statistics['cancelled'] }}</div>
            <div class="stat-label">ملغية</div>
        </div>
        @endif
        <div class="stat-item">
            <div class="stat-number">{{ number_format($statistics['total_amount'], 2) }}</div>
            <div class="stat-label">إجمالي المبلغ (ريال)</div>
        </div>
    </div>

    <!-- جدول البيانات -->
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th style="width: 12%">رقم الفاتورة</th>
                    <th style="width: 12%">التاريخ</th>
                    <th style="width: 15%">العميل</th>
                    <th style="width: 12%">المستودع</th>
                    <th style="width: 12%">المبلغ الإجمالي</th>
                    <th style="width: 15%">طريقة الدفع</th>
                    <th style="width: 12%">منشئ الفاتورة</th>
                    <th style="width: 10%">الحالة</th>
                </tr>
            </thead>
            <tbody>
                @foreach($posPayments as $pos)
                <tr>
                    <td>{{ $pos->pos_id }}</td>
                    <td>{{ \Carbon\Carbon::parse($pos->pos_date)->format('Y-m-d') }}</td>
                    <td>{{ $pos->customer ? $pos->customer->name : 'عميل نقدي' }}</td>
                    <td>{{ $pos->warehouse ? $pos->warehouse->name : '-' }}</td>
                    <td class="amount">
                        @if($pos->posPayment)
                            {{ number_format($pos->posPayment->amount, 2) }} ريال
                        @else
                            0.00 ريال
                        @endif
                    </td>
                    <td>
                        @if($pos->posPayment)
                            @if($pos->posPayment->payment_type == 'cash')
                                نقد
                            @elseif($pos->posPayment->payment_type == 'network')
                                شبكة
                            @elseif($pos->posPayment->payment_type == 'split')
                                دفع مقسم
                            @else
                                {{ $pos->posPayment->payment_type }}
                            @endif
                        @else
                            نقد
                        @endif
                    </td>
                    <td>{{ $pos->createdBy ? $pos->createdBy->name : '-' }}</td>
                    <td>
                        @if($pos->status_type == 'returned')
                            <span class="status-returned">مرتجع بضاعة</span>
                        @elseif($pos->status_type == 'cancelled')
                            <span class="status-cancelled">ملغية</span>
                        @elseif(!empty($pos->customer) && $pos->customer->is_delivery)
                            @if($pos->is_payment_set)
                                <span class="status-normal">تم التحصيل</span>
                            @else
                                <span class="status-delivery">جاري التحصيل</span>
                            @endif
                        @else
                            <span class="status-normal">مكتملة</span>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- تذييل التقرير -->
    <div class="footer">
        <div>تم إنشاء هذا التقرير بواسطة نظام إدارة نقاط البيع</div>
        <div>إجمالي عدد السجلات: {{ count($posPayments) }} فاتورة</div>
        <div>تاريخ الطباعة: {{ now()->format('Y-m-d H:i:s') }}</div>
        <div>جميع الحقوق محفوظة © {{ date('Y') }}</div>
    </div>
</body>
</html>
