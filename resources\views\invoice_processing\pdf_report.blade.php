<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>تقرير تسويات مبيعات نقاط البيع</title>
    <style>
        
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            color: #333;
            direction: rtl;
            text-align: right;
            margin: 10px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
        }

        .header h1 {
            font-size: 20px;
            color: #007bff;
            margin-bottom: 10px;
        }

        .header .report-info {
            font-size: 12px;
            color: #666;
        }
        
        .filters-section {
            background: #f8f9fa;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
        }

        .filters-title {
            font-weight: bold;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .filter-item {
            margin-bottom: 3px;
            font-size: 11px;
        }

        .statistics-section {
            margin-bottom: 15px;
            background: #f8f9fa;
            padding: 10px;
            border: 1px solid #ccc;
        }

        .statistics-row {
            width: 100%;
            border-collapse: collapse;
        }

        .statistics-row td {
            text-align: center;
            padding: 8px;
            border: 1px solid #ccc;
        }

        .stat-number {
            font-size: 14px;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            font-size: 10px;
            color: #666;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 9px;
            margin-bottom: 15px;
        }

        th, td {
            border: 1px solid #ccc;
            padding: 5px;
            text-align: center;
        }

        th {
            background: #343a40;
            color: white;
            font-weight: bold;
            font-size: 10px;
        }

        tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .amount {
            font-weight: bold;
            color: #28a745;
        }

        .status-normal {
            color: #28a745;
        }

        .status-returned {
            color: #dc3545;
        }

        .status-cancelled {
            color: #6c757d;
        }

        .status-delivery {
            color: #ffc107;
        }
        
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 9px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <!-- رأس التقرير -->
    <div class="header">
        <h1>تقرير تسويات مبيعات نقاط البيع</h1>
        <div class="report-info">
            <div>تاريخ إنشاء التقرير: {{ \Carbon\Carbon::parse($reportDate)->format('Y-m-d H:i:s') }}</div>
            <div>المستخدم: {{ Auth::user()->name }}</div>
            @if(request('date_from') || request('date_to'))
                <div style="margin-top: 5px; font-weight: 600; color: #007bff;">
                    فترة التقرير:
                    @if(request('date_from') && request('date_to'))
                        من {{ request('date_from') }} إلى {{ request('date_to') }}
                    @elseif(request('date_from'))
                        من {{ request('date_from') }}
                    @elseif(request('date_to'))
                        حتى {{ request('date_to') }}
                    @endif
                </div>
            @endif
        </div>
    </div>

    <!-- قسم الفلاتر المطبقة -->
    @if(!empty($appliedFilters))
    <div class="filters-section">
        <div class="filters-title">الفلاتر المطبقة:</div>
        @foreach($appliedFilters as $filter)
            <div class="filter-item">• {{ $filter }}</div>
        @endforeach
    </div>
    @endif

    <!-- قسم الإحصائيات -->
    <div class="statistics-section">
        <table class="statistics-row">
            <tr>
                <td>
                    <div class="stat-number">{{ $statistics['total'] }}</div>
                    <div class="stat-label">إجمالي الفواتير</div>
                </td>
                <td>
                    <div class="stat-number">{{ $statistics['completed'] }}</div>
                    <div class="stat-label">مكتملة</div>
                </td>
                @if($statistics['delivery_in_progress'] > 0)
                <td>
                    <div class="stat-number">{{ $statistics['delivery_in_progress'] }}</div>
                    <div class="stat-label">جاري التوصيل</div>
                </td>
                @endif
                @if($statistics['returned'] > 0)
                <td>
                    <div class="stat-number">{{ $statistics['returned'] }}</div>
                    <div class="stat-label">مرتجعة</div>
                </td>
                @endif
                @if($statistics['cancelled'] > 0)
                <td>
                    <div class="stat-number">{{ $statistics['cancelled'] }}</div>
                    <div class="stat-label">ملغية</div>
                </td>
                @endif
                <td>
                    <div class="stat-number">{{ number_format($statistics['total_amount'], 2) }}</div>
                    <div class="stat-label">إجمالي المبلغ (ريال)</div>
                </td>
            </tr>
        </table>
    </div>

    <!-- جدول البيانات -->
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th style="width: 12%">رقم الفاتورة</th>
                    <th style="width: 12%">التاريخ</th>
                    <th style="width: 15%">العميل</th>
                    <th style="width: 12%">المستودع</th>
                    <th style="width: 12%">المبلغ الإجمالي</th>
                    <th style="width: 15%">طريقة الدفع</th>
                    <th style="width: 12%">منشئ الفاتورة</th>
                    <th style="width: 10%">الحالة</th>
                </tr>
            </thead>
            <tbody>
                @foreach($posPayments as $pos)
                <tr>
                    <td>{{ $pos->pos_id }}</td>
                    <td>{{ \Carbon\Carbon::parse($pos->pos_date)->format('Y-m-d') }}</td>
                    <td>{{ $pos->customer ? $pos->customer->name : 'عميل نقدي' }}</td>
                    <td>{{ $pos->warehouse ? $pos->warehouse->name : '-' }}</td>
                    <td class="amount">
                        @if($pos->posPayment)
                            {{ number_format($pos->posPayment->amount, 2) }} ريال
                        @else
                            0.00 ريال
                        @endif
                    </td>
                    <td>
                        @if($pos->posPayment)
                            @if($pos->posPayment->payment_type == 'cash')
                                نقد
                            @elseif($pos->posPayment->payment_type == 'network')
                                شبكة
                            @elseif($pos->posPayment->payment_type == 'split')
                                دفع مقسم
                            @else
                                {{ $pos->posPayment->payment_type }}
                            @endif
                        @else
                            نقد
                        @endif
                    </td>
                    <td>{{ $pos->createdBy ? $pos->createdBy->name : '-' }}</td>
                    <td>
                        @if($pos->status_type == 'returned')
                            <span class="status-returned">مرتجع بضاعة</span>
                        @elseif($pos->status_type == 'cancelled')
                            <span class="status-cancelled">ملغية</span>
                        @elseif(!empty($pos->customer) && $pos->customer->is_delivery)
                            @if($pos->is_payment_set)
                                <span class="status-normal">تم التحصيل</span>
                            @else
                                <span class="status-delivery">جاري التحصيل</span>
                            @endif
                        @else
                            <span class="status-normal">مكتملة</span>
                        @endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- تذييل التقرير -->
    <div class="footer">
        <div>تم إنشاء هذا التقرير بواسطة نظام إدارة نقاط البيع</div>
        <div>إجمالي عدد السجلات: {{ count($posPayments) }} فاتورة</div>
        <div>تاريخ الطباعة: {{ now()->format('Y-m-d H:i:s') }}</div>
        <div>جميع الحقوق محفوظة © {{ date('Y') }}</div>
    </div>
</body>
</html>
