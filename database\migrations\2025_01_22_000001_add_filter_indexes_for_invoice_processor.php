<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إضافة فهارس إضافية لتحسين أداء الفلترة في معالج الفواتير
        
        // فهارس لجدول pos
        Schema::table('pos', function (Blueprint $table) {
            // فهرس لفلتر العميل
            if (!$this->indexExists('pos', 'idx_pos_customer_id')) {
                $table->index('customer_id', 'idx_pos_customer_id');
            }
            
            // فهرس لفلتر حالة الفاتورة
            if (!$this->indexExists('pos', 'idx_pos_status_type')) {
                $table->index('status_type', 'idx_pos_status_type');
            }
            
            // فهرس مركب للفلترة المتقدمة
            if (!$this->indexExists('pos', 'idx_pos_filter_combo')) {
                $table->index(['customer_id', 'warehouse_id', 'status_type'], 'idx_pos_filter_combo');
            }
            
            // فهرس لتاريخ الفاتورة
            if (!$this->indexExists('pos', 'idx_pos_date')) {
                $table->index('pos_date', 'idx_pos_date');
            }
        });

        // فهارس لجدول pos_payments
        Schema::table('pos_payments', function (Blueprint $table) {
            // فهرس لطريقة الدفع
            if (!$this->indexExists('pos_payments', 'idx_pos_payments_type')) {
                $table->index('payment_type', 'idx_pos_payments_type');
            }
            
            // فهرس مركب لطريقة الدفع والمبلغ
            if (!$this->indexExists('pos_payments', 'idx_pos_payments_type_amount')) {
                $table->index(['payment_type', 'amount'], 'idx_pos_payments_type_amount');
            }
        });

        // فهارس لجدول customers
        Schema::table('customers', function (Blueprint $table) {
            // فهرس لاسم العميل
            if (!$this->indexExists('customers', 'idx_customers_name')) {
                $table->index('name', 'idx_customers_name');
            }
            
            // فهرس مركب للبحث السريع
            if (!$this->indexExists('customers', 'idx_customers_search')) {
                $table->index(['created_by', 'name'], 'idx_customers_search');
            }
        });

        // فهارس لجدول warehouses
        Schema::table('warehouses', function (Blueprint $table) {
            // فهرس لاسم المستودع
            if (!$this->indexExists('warehouses', 'idx_warehouses_name')) {
                $table->index('name', 'idx_warehouses_name');
            }
            
            // فهرس مركب للبحث السريع
            if (!$this->indexExists('warehouses', 'idx_warehouses_search')) {
                $table->index(['created_by', 'name'], 'idx_warehouses_search');
            }
        });

        // فهارس لجدول users
        Schema::table('users', function (Blueprint $table) {
            // فهرس لاسم المستخدم
            if (!$this->indexExists('users', 'idx_users_name')) {
                $table->index('name', 'idx_users_name');
            }
            
            // فهرس مركب للمنشئين
            if (!$this->indexExists('users', 'idx_users_creator_search')) {
                $table->index(['created_by', 'name'], 'idx_users_creator_search');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // حذف الفهارس المضافة
        
        Schema::table('pos', function (Blueprint $table) {
            $this->dropIndexIfExists($table, 'idx_pos_customer_id');
            $this->dropIndexIfExists($table, 'idx_pos_status_type');
            $this->dropIndexIfExists($table, 'idx_pos_filter_combo');
            $this->dropIndexIfExists($table, 'idx_pos_date');
        });

        Schema::table('pos_payments', function (Blueprint $table) {
            $this->dropIndexIfExists($table, 'idx_pos_payments_type');
            $this->dropIndexIfExists($table, 'idx_pos_payments_type_amount');
        });

        Schema::table('customers', function (Blueprint $table) {
            $this->dropIndexIfExists($table, 'idx_customers_name');
            $this->dropIndexIfExists($table, 'idx_customers_search');
        });

        Schema::table('warehouses', function (Blueprint $table) {
            $this->dropIndexIfExists($table, 'idx_warehouses_name');
            $this->dropIndexIfExists($table, 'idx_warehouses_search');
        });

        Schema::table('users', function (Blueprint $table) {
            $this->dropIndexIfExists($table, 'idx_users_name');
            $this->dropIndexIfExists($table, 'idx_users_creator_search');
        });
    }

    /**
     * التحقق من وجود فهرس
     */
    private function indexExists(string $table, string $index): bool
    {
        $indexes = Schema::getConnection()
            ->getDoctrineSchemaManager()
            ->listTableIndexes($table);
        
        return array_key_exists($index, $indexes);
    }

    /**
     * حذف فهرس إذا كان موجوداً
     */
    private function dropIndexIfExists(Blueprint $table, string $index): void
    {
        $tableName = $table->getTable();
        if ($this->indexExists($tableName, $index)) {
            $table->dropIndex($index);
        }
    }
};
