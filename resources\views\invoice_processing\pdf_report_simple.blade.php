<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>تقرير تسويات مبيعات نقاط البيع</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            color: #333;
            direction: rtl;
            text-align: right;
            margin: 10px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
        }
        
        .header h1 {
            font-size: 18px;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10px;
            margin-bottom: 15px;
        }
        
        th, td {
            border: 1px solid #ccc;
            padding: 5px;
            text-align: center;
        }
        
        th {
            background: #343a40;
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 9px;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <!-- رأس التقرير -->
    <div class="header">
        <h1>تقرير تسويات مبيعات نقاط البيع</h1>
        <div>تاريخ إنشاء التقرير: {{ $reportDate }}</div>
        <div>المستخدم: {{ Auth::user()->name }}</div>
    </div>

    <!-- إحصائيات بسيطة -->
    <div style="background: #f8f9fa; padding: 10px; margin-bottom: 15px; border: 1px solid #ccc;">
        <strong>الإحصائيات:</strong>
        إجمالي الفواتير: {{ $statistics['total'] }} | 
        مكتملة: {{ $statistics['completed'] }} | 
        إجمالي المبلغ: {{ number_format($statistics['total_amount'], 2) }} ريال
    </div>

    <!-- جدول البيانات -->
    <table>
        <thead>
            <tr>
                <th>رقم الفاتورة</th>
                <th>التاريخ</th>
                <th>العميل</th>
                <th>المستودع</th>
                <th>المبلغ الإجمالي</th>
                <th>طريقة الدفع</th>
                <th>منشئ الفاتورة</th>
                <th>الحالة</th>
            </tr>
        </thead>
        <tbody>
            @foreach($posPayments as $pos)
            <tr>
                <td>{{ $pos->pos_id }}</td>
                <td>{{ \Carbon\Carbon::parse($pos->pos_date)->format('Y-m-d') }}</td>
                <td>{{ $pos->customer ? $pos->customer->name : 'عميل نقدي' }}</td>
                <td>{{ $pos->warehouse ? $pos->warehouse->name : '-' }}</td>
                <td>
                    @if($pos->posPayment)
                        {{ number_format($pos->posPayment->amount, 2) }} ريال
                    @else
                        0.00 ريال
                    @endif
                </td>
                <td>
                    @if($pos->posPayment)
                        @if($pos->posPayment->payment_type == 'cash')
                            نقد
                        @elseif($pos->posPayment->payment_type == 'network')
                            شبكة
                        @elseif($pos->posPayment->payment_type == 'split')
                            دفع مقسم
                        @else
                            {{ $pos->posPayment->payment_type }}
                        @endif
                    @else
                        نقد
                    @endif
                </td>
                <td>{{ $pos->createdBy ? $pos->createdBy->name : '-' }}</td>
                <td>
                    @if($pos->status_type == 'returned')
                        مرتجع بضاعة
                    @elseif($pos->status_type == 'cancelled')
                        ملغية
                    @elseif(!empty($pos->customer) && $pos->customer->is_delivery)
                        @if($pos->is_payment_set)
                            تم التحصيل
                        @else
                            جاري التحصيل
                        @endif
                    @else
                        مكتملة
                    @endif
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>

    <!-- تذييل التقرير -->
    <div class="footer">
        <div>تم إنشاء هذا التقرير بواسطة نظام إدارة نقاط البيع</div>
        <div>إجمالي عدد السجلات: {{ count($posPayments) }} فاتورة</div>
        <div>تاريخ الطباعة: {{ now()->format('Y-m-d H:i:s') }}</div>
    </div>
</body>
</html>
