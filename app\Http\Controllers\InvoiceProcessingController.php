<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Invoice;
use App\Models\InvoicePayment;
use App\Models\Pos;
use App\Models\PosPayment;
use App\Models\PosProduct;
use App\Models\User;
use App\Models\warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;

class InvoiceProcessingController extends Controller
{
    /**
     * عرض قائمة فواتير نقاط البيع
     */
    public function index()
    {
        // استخدام نفس طريقة استرجاع الفواتير كما في تقارير نقاط البيع
        $posPayments = Pos::where('created_by', '=', \Auth::user()->creatorId())
            ->with(['customer', 'warehouse', 'posPayment'])
            ->orderBy('id', 'desc')
            ->get();

        // إضافة معلومات الدفع لكل فاتورة
        foreach ($posPayments as $pos) {
            if ($pos->posPayment) {
                $pos->payment_method = $pos->posPayment->payment_type;
            } else {
                $pos->payment_method = 'cash';
            }
        }

        return view('invoice_processing.index', compact('posPayments'));
    }

    /**
     * عرض تفاصيل فاتورة محددة
     */
    public function show($ids)
    {
        try {
            $id = Crypt::decrypt($ids);
        } catch (\Throwable $th) {
            // إذا فشل فك التشفير، نستخدم المعرف كما هو
            $id = $ids;
        }

        $pos = Pos::find($id);

        if (!$pos) {
            return redirect()->route('invoice.processing.index')->with('error', __('الفاتورة غير موجودة.'));
        }

        $posPayment = PosPayment::where('pos_id', $pos->id)->first();
        $customer = $pos->customer;
        $items = $pos->items;

        // إذا لم يكن هناك معلومات دفع، نقوم بإنشاء كائن فارغ
        if (!$posPayment) {
            $posPayment = new PosPayment();
            $posPayment->payment_type = 'cash';
            $posPayment->amount = $pos->getTotal();
            $posPayment->discount = $pos->getTotalDiscount();
            $posPayment->date = $pos->pos_date;
        }

        return view('invoice_processing.show', compact('pos', 'customer', 'items', 'posPayment'));
    }

    /**
     * تصدير الفاتورة كملف PDF
     */
    public function pdf($ids)
    {
        try {
            $id = Crypt::decrypt($ids);
        } catch (\Throwable $th) {
            // إذا فشل فك التشفير، قد يكون المعرف غير مشفر
            $id = $ids;
        }

        $pos = Pos::find($id);

        if (!$pos) {
            return redirect()->route('invoice.processing.index')->with('error', __('الفاتورة غير موجودة.'));
        }

        try {
            // استخدام نفس طريقة عرض PDF الموجودة في النظام
            return redirect()->route('pos.pdf', Crypt::encrypt($pos->id));
        } catch (\Throwable $th) {
            return redirect()->route('invoice.processing.index')->with('error', __('حدث خطأ أثناء إنشاء ملف PDF.'));
        }
    }

    /**
     * عرض ملخص فواتير نقاط البيع (POS Summary)
     */
    public function summary()
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // استرجاع جميع فواتير نقاط البيع للمستخدم الحالي
        $posPayments = Pos::where('created_by', '=', \Auth::user()->creatorId())
            ->with(['customer', 'warehouse', 'posPayment'])
            ->orderBy('id', 'desc')
            ->get();

        // إضافة معلومات الدفع لكل فاتورة
        foreach ($posPayments as $pos) {
            if ($pos->posPayment) {
                $pos->payment_method = $pos->posPayment->payment_type;
            } else {
                $pos->payment_method = 'cash';
            }
        }

        return view('invoice_processing.summary', compact('posPayments'));
    }

    /**
     * عرض ملخص فواتير نقاط البيع في قسم إدارة العمليات المالية (POS Summary in Financial Operations)
     */
    public function posSummary()
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // استرجاع جميع فواتير نقاط البيع للمستخدم الحالي
        $posPayments = Pos::where('created_by', '=', \Auth::user()->creatorId())
            ->with(['customer', 'warehouse', 'posPayment', 'createdBy'])
            ->orderBy('id', 'desc')
            ->get();

        // إضافة معلومات الدفع لكل فاتورة
        foreach ($posPayments as $pos) {
            if ($pos->posPayment) {
                $pos->payment_method = $pos->posPayment->payment_type;
            } else {
                $pos->payment_method = 'cash';
            }
        }

        return view('invoice_processing.pos_summary', compact('posPayments'));
    }

    /**
     * عرض معالج فواتير البيع في قسم إدارة العمليات المالية
     */
    public function invoiceProcessor(Request $request)
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // بناء الاستعلام الأساسي
        $query = Pos::with(['customer', 'warehouse', 'posPayment', 'createdBy', 'shift', 'shift.creator']);

        // تطبيق الفلاتر
        $this->applyFilters($query, $request);

        // استرجاع البيانات مع الترتيب
        $posPayments = $query->orderBy('id', 'desc')->get();

        // جمع كل معرفات المستخدمين المرتبطة بالفواتير
        $userIds = $posPayments->pluck('user_id')->filter()->unique()->toArray();

        // استرجاع بيانات المستخدمين دفعة واحدة
        $users = [];
        if (!empty($userIds)) {
            $usersCollection = \App\Models\User::whereIn('id', $userIds)->get();
            foreach ($usersCollection as $user) {
                $users[$user->id] = $user;
            }
        }

        // إضافة معلومات الدفع والمستخدم لكل فاتورة
        foreach ($posPayments as $pos) {
            // إضافة معلومات الدفع
            if ($pos->posPayment) {
                $pos->payment_method = $pos->posPayment->payment_type;
            } else {
                $pos->payment_method = 'cash';
            }

            // إضافة معلومات المستخدم
            if (!empty($pos->user_id) && isset($users[$pos->user_id])) {
                $pos->user = $users[$pos->user_id];
            } else {
                $pos->user = null;
            }
        }

        // استرجاع بيانات الفلاتر للعرض
        $filterData = $this->getFilterData();

        return view('invoice_processing.invoice_processor', compact('posPayments', 'filterData'));
    }

    /**
     * تطبيق الفلاتر على الاستعلام
     */
    private function applyFilters($query, Request $request)
    {
        // فلتر العميل
        if ($request->filled('customer_id') && $request->customer_id != '') {
            $query->where('customer_id', $request->customer_id);
        }

        // فلتر التاريخ من
        if ($request->filled('date_from')) {
            $query->whereDate('pos_date', '>=', $request->date_from);
        }

        // فلتر التاريخ إلى
        if ($request->filled('date_to')) {
            $query->whereDate('pos_date', '<=', $request->date_to);
        }

        // فلتر المستودع
        if ($request->filled('warehouse_id') && $request->warehouse_id != '') {
            $query->where('warehouse_id', $request->warehouse_id);
        }

        // فلتر طريقة الدفع (يمكن اختيار أكثر من واحدة)
        if ($request->filled('payment_methods') && is_array($request->payment_methods)) {
            $paymentMethods = array_filter($request->payment_methods);
            if (!empty($paymentMethods)) {
                // فصل طرق الدفع العادية عن حالات الفواتير
                $normalPaymentMethods = [];
                $statusTypes = [];

                foreach ($paymentMethods as $method) {
                    if (in_array($method, ['returned', 'cancelled'])) {
                        $statusTypes[] = $method;
                    } else {
                        $normalPaymentMethods[] = $method;
                    }
                }

                // تطبيق الفلاتر
                $query->where(function($q) use ($normalPaymentMethods, $statusTypes) {
                    // فلتر طرق الدفع العادية
                    if (!empty($normalPaymentMethods)) {
                        $q->orWhereHas('posPayment', function($subQ) use ($normalPaymentMethods) {
                            $subQ->whereIn('payment_type', $normalPaymentMethods);
                        });
                    }

                    // فلتر حالات الفواتير
                    if (!empty($statusTypes)) {
                        $q->orWhereIn('status_type', $statusTypes);
                    }
                });
            }
        }

        // فلتر منشئ الفاتورة
        if ($request->filled('created_by') && $request->created_by != '') {
            $query->where('created_by', $request->created_by);
        }
    }

    /**
     * استرجاع بيانات الفلاتر للعرض في القوائم المنسدلة
     */
    private function getFilterData()
    {
        return [
            'customers' => Customer::where('created_by', \Auth::user()->creatorId())
                ->orderBy('name')
                ->get(['id', 'name']),

            'warehouses' => warehouse::where('created_by', \Auth::user()->creatorId())
                ->orderBy('name')
                ->get(['id', 'name']),

            'creators' => User::where('created_by', \Auth::user()->creatorId())
                ->orWhere('id', \Auth::user()->creatorId())
                ->orderBy('name')
                ->get(['id', 'name']),

            'payment_methods' => [
                'cash' => __('نقد') . ' 💵',
                'network' => __('شبكة') . ' 💳',
                'split' => __('دفع مقسم') . ' 💳 💵',
                'returned' => __('مرتجع بضاعة') . ' ↩️',
                'cancelled' => __('ملغية') . ' ❌'
            ]
        ];
    }

    /**
     * إنشاء تقرير PDF لتسويات مبيعات نقاط البيع
     */
    public function generatePDF(Request $request)
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        // بناء الاستعلام مع نفس الفلاتر
        $query = Pos::with(['customer', 'warehouse', 'posPayment', 'createdBy', 'shift', 'shift.creator']);
        $this->applyFilters($query, $request);
        $posPayments = $query->orderBy('id', 'desc')->get();

        // جمع معرفات المستخدمين
        $userIds = $posPayments->pluck('user_id')->filter()->unique()->toArray();
        $users = [];
        if (!empty($userIds)) {
            $usersCollection = \App\Models\User::whereIn('id', $userIds)->get();
            foreach ($usersCollection as $user) {
                $users[$user->id] = $user;
            }
        }

        // إضافة معلومات الدفع والمستخدم
        foreach ($posPayments as $pos) {
            if ($pos->posPayment) {
                $pos->payment_method = $pos->posPayment->payment_type;
            } else {
                $pos->payment_method = 'cash';
            }

            if (!empty($pos->user_id) && isset($users[$pos->user_id])) {
                $pos->user = $users[$pos->user_id];
            } else {
                $pos->user = null;
            }
        }

        // حساب الإحصائيات
        $statistics = $this->calculateStatistics($posPayments);

        // إعداد بيانات الفلاتر المطبقة
        $appliedFilters = $this->getAppliedFilters($request);

        // إنشاء PDF باستخدام DomPDF
        $html = view('invoice_processing.pdf_report', [
            'posPayments' => $posPayments,
            'statistics' => $statistics,
            'appliedFilters' => $appliedFilters,
            'reportDate' => now()->format('Y-m-d H:i:s')
        ])->render();

        // إنشاء PDF
        $dompdf = new \Dompdf\Dompdf();
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'landscape');
        $dompdf->render();

        // إنشاء اسم الملف بناءً على الفلاتر المطبقة
        $fileName = $this->generateFileName($request);

        return response()->streamDownload(function() use ($dompdf) {
            echo $dompdf->output();
        }, $fileName, [
            'Content-Type' => 'application/pdf',
        ]);
    }

    /**
     * حساب الإحصائيات
     */
    private function calculateStatistics($posPayments)
    {
        $statistics = [
            'total' => $posPayments->count(),
            'completed' => 0,
            'delivery_in_progress' => 0,
            'returned' => 0,
            'cancelled' => 0,
            'total_amount' => 0
        ];

        foreach ($posPayments as $pos) {
            // حساب المبلغ الإجمالي
            if ($pos->posPayment) {
                $statistics['total_amount'] += $pos->posPayment->amount;
            }

            // تصنيف الحالات
            if ($pos->status_type == 'returned') {
                $statistics['returned']++;
            } elseif ($pos->status_type == 'cancelled') {
                $statistics['cancelled']++;
            } elseif (!empty($pos->customer) && $pos->customer->is_delivery && !$pos->is_payment_set) {
                $statistics['delivery_in_progress']++;
            } else {
                $statistics['completed']++;
            }
        }

        return $statistics;
    }

    /**
     * الحصول على الفلاتر المطبقة
     */
    private function getAppliedFilters(Request $request)
    {
        $filters = [];

        if ($request->filled('customer_id')) {
            $customer = Customer::find($request->customer_id);
            if ($customer) {
                $filters[] = 'العميل: ' . $customer->name;
            }
        }

        if ($request->filled('warehouse_id')) {
            $warehouse = warehouse::find($request->warehouse_id);
            if ($warehouse) {
                $filters[] = 'المستودع: ' . $warehouse->name;
            }
        }

        if ($request->filled('created_by')) {
            $creator = \App\Models\User::find($request->created_by);
            if ($creator) {
                $filters[] = 'منشئ الفاتورة: ' . $creator->name;
            }
        }

        if ($request->filled('date_from')) {
            $filters[] = 'من تاريخ: ' . $request->date_from;
        }

        if ($request->filled('date_to')) {
            $filters[] = 'إلى تاريخ: ' . $request->date_to;
        }

        if ($request->filled('payment_methods') && is_array($request->payment_methods)) {
            $paymentMethods = [];
            $methodLabels = [
                'cash' => 'نقد',
                'network' => 'شبكة',
                'split' => 'دفع مقسم',
                'returned' => 'مرتجع بضاعة',
                'cancelled' => 'ملغية'
            ];

            foreach ($request->payment_methods as $method) {
                if (isset($methodLabels[$method])) {
                    $paymentMethods[] = $methodLabels[$method];
                }
            }

            if (!empty($paymentMethods)) {
                $filters[] = 'طرق الدفع/الحالة: ' . implode(', ', $paymentMethods);
            }
        }

        return $filters;
    }

    /**
     * إنشاء اسم الملف بناءً على الفلاتر المطبقة
     */
    private function generateFileName(Request $request)
    {
        $fileName = 'تقرير_تسويات_مبيعات_نقاط_البيع';

        // إضافة التواريخ إذا كانت موجودة
        $dateFrom = $request->filled('date_from') ? $request->date_from : null;
        $dateTo = $request->filled('date_to') ? $request->date_to : null;

        if ($dateFrom && $dateTo) {
            // إذا كان هناك تاريخ من وإلى
            $fileName .= '_من_' . str_replace('-', '_', $dateFrom) . '_إلى_' . str_replace('-', '_', $dateTo);
        } elseif ($dateFrom) {
            // إذا كان هناك تاريخ من فقط
            $fileName .= '_من_' . str_replace('-', '_', $dateFrom);
        } elseif ($dateTo) {
            // إذا كان هناك تاريخ إلى فقط
            $fileName .= '_حتى_' . str_replace('-', '_', $dateTo);
        } else {
            // إذا لم يكن هناك تواريخ، استخدم التاريخ الحالي
            $fileName .= '_' . now()->format('Y_m_d');
        }

        // إضافة معلومات إضافية إذا كانت موجودة
        if ($request->filled('customer_id')) {
            $customer = Customer::find($request->customer_id);
            if ($customer) {
                $customerName = str_replace(' ', '_', $customer->name);
                $fileName .= '_عميل_' . $customerName;
            }
        }

        if ($request->filled('warehouse_id')) {
            $warehouse = warehouse::find($request->warehouse_id);
            if ($warehouse) {
                $warehouseName = str_replace(' ', '_', $warehouse->name);
                $fileName .= '_مستودع_' . $warehouseName;
            }
        }

        // إضافة الوقت لضمان عدم التكرار
        $fileName .= '_' . now()->format('H_i_s');

        return $fileName . '.pdf';
    }

    /**
     * تغيير حالة الفاتورة إلى مرتجع
     */
    public function returnInvoice($id)
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        try {
            // تحديث حالة الفاتورة إلى 'returned'
            $pos = Pos::find($id);

            if (!$pos) {
                return redirect()->back()->with('error', __('الفاتورة غير موجودة.'));
            }

            $pos->status_type = 'returned';
            $pos->save();

            return redirect()->back()->with('success', __('تم تحديث حالة الفاتورة إلى مرتجع بضاعة بنجاح'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * تغيير حالة الفاتورة إلى ملغية
     */
    public function cancelInvoice($id)
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        try {
            // تحديث حالة الفاتورة إلى 'cancelled'
            $pos = Pos::find($id);

            if (!$pos) {
                return redirect()->back()->with('error', __('الفاتورة غير موجودة.'));
            }

            $pos->status_type = 'cancelled';
            $pos->save();

            return redirect()->back()->with('success', __('تم تحديث حالة الفاتورة إلى ملغية بنجاح'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * عرض صفحة تعديل منتجات الفاتورة
     */
    public function editProducts($id)
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        try {
            // استرجاع الفاتورة مع المنتجات والعلاقات
            $pos = Pos::with(['customer', 'warehouse', 'items.product', 'posPayment'])
                ->find($id);

            if (!$pos) {
                return redirect()->back()->with('error', __('الفاتورة غير موجودة.'));
            }

            // استرجاع جميع المنتجات المتاحة للإضافة مع كمياتها في المستودع
            $products = \App\Models\ProductService::where('created_by', \Auth::user()->creatorId())
                ->where('type', 'product')
                ->with(['taxes', 'warehouseProducts' => function($query) use ($pos) {
                    $query->where('warehouse_id', $pos->warehouse_id);
                }])
                ->get();

            // إضافة الكمية المتاحة لكل منتج
            foreach ($products as $product) {
                $warehouseProduct = $product->warehouseProducts->first();
                $product->available_quantity = $warehouseProduct ? $warehouseProduct->quantity : 0;
            }

            return view('invoice_processing.edit_products', compact('pos', 'products'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * تحديث منتجات الفاتورة ومعلومات الدفع
     */
    public function updateProducts($id, Request $request)
    {
        if (!\Auth::user()->can('manage pos')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        try {
            $pos = Pos::find($id);

            if (!$pos) {
                return redirect()->back()->with('error', __('الفاتورة غير موجودة.'));
            }

            // التحقق من صحة البيانات
            $request->validate([
                'products' => 'required|array',
                'products.*.product_id' => 'required|exists:product_services,id',
                'products.*.quantity' => 'required|numeric|min:1',
                'products.*.price' => 'required|numeric|min:0',
                'products.*.discount' => 'nullable|numeric|min:0',
                'products.*.tax' => 'nullable|string',
                'products.*.description' => 'nullable|string|max:255',
                // معلومات الدفع
                'payment_type' => 'required|string|in:cash,network,split',
                'cash_amount' => 'nullable|numeric|min:0',
                'network_amount' => 'nullable|numeric|min:0',
                'transaction_number' => 'nullable|string|max:255',
                'payment_date' => 'required|date',
                'payment_discount' => 'nullable|numeric|min:0',
            ]);

            // التحقق من توفر المخزون للمنتجات الجديدة
            $stockErrors = [];
            foreach ($request->products as $index => $productData) {
                $product = \App\Models\ProductService::find($productData['product_id']);
                if (!$product) {
                    continue;
                }

                // الحصول على الكمية الحالية في المستودع
                $warehouseProduct = \App\Models\WarehouseProduct::where('warehouse_id', $pos->warehouse_id)
                    ->where('product_id', $productData['product_id'])
                    ->first();

                $currentStock = $warehouseProduct ? $warehouseProduct->quantity : 0;

                // الحصول على الكمية الحالية للمنتج في الفاتورة (إن وجدت)
                $currentPosProduct = \App\Models\PosProduct::where('pos_id', $pos->id)
                    ->where('product_id', $productData['product_id'])
                    ->first();

                $currentPosQuantity = $currentPosProduct ? $currentPosProduct->quantity : 0;

                // الكمية المتاحة = المخزون الحالي + الكمية المستخدمة حالياً في الفاتورة
                $availableStock = $currentStock + $currentPosQuantity;

                if ($productData['quantity'] > $availableStock) {
                    $stockErrors[] = "المنتج '{$product->name}' - الكمية المطلوبة ({$productData['quantity']}) أكبر من المتاح ({$availableStock})";
                }
            }

            if (!empty($stockErrors)) {
                return redirect()->back()
                    ->withErrors(['stock' => $stockErrors])
                    ->withInput();
            }

            // استرجاع المنتجات الحالية قبل الحذف لإعادة المخزون
            $currentProducts = \App\Models\PosProduct::where('pos_id', $pos->id)->get();

            // إعادة المخزون للمنتجات الحالية
            foreach ($currentProducts as $currentProduct) {
                \App\Models\Utility::warehouse_quantity(
                    'plus',
                    $currentProduct->quantity,
                    $currentProduct->product_id,
                    $pos->warehouse_id
                );

                // إضافة سجل في تقرير المخزون
                \App\Models\Utility::addProductStock(
                    $currentProduct->product_id,
                    $currentProduct->quantity,
                    'pos_edit_return',
                    'إعادة مخزون بسبب تعديل فاتورة رقم ' . $pos->id,
                    $pos->id
                );
            }

            // حذف المنتجات الحالية
            \App\Models\PosProduct::where('pos_id', $pos->id)->delete();

            // إضافة المنتجات الجديدة وخصم المخزون
            foreach ($request->products as $productData) {
                // التأكد من وجود المنتج المحدد
                $product = \App\Models\ProductService::find($productData['product_id']);
                if (!$product) {
                    continue; // تخطي المنتج إذا لم يكن موجوداً
                }

                // إنشاء منتج الفاتورة الجديد
                \App\Models\PosProduct::create([
                    'pos_id' => $pos->id,
                    'product_id' => $productData['product_id'],
                    'quantity' => $productData['quantity'],
                    'price' => $productData['price'],
                    'discount' => $productData['discount'] ?? 0,
                    'tax' => $productData['tax'] ?? '0.00',
                    'description' => $productData['description'] ?? null,
                ]);

                // خصم المخزون للمنتج الجديد
                \App\Models\Utility::warehouse_quantity(
                    'minus',
                    $productData['quantity'],
                    $productData['product_id'],
                    $pos->warehouse_id
                );

                // إضافة سجل في تقرير المخزون
                \App\Models\Utility::addProductStock(
                    $productData['product_id'],
                    $productData['quantity'],
                    'pos_edit_sale',
                    'خصم مخزون بسبب تعديل فاتورة رقم ' . $pos->id,
                    $pos->id
                );
            }

            // تحديث معلومات الدفع
            $this->updatePaymentInfo($pos, $request);

            // تحديث تاريخ آخر تعديل للفاتورة
            $pos->touch();

            return redirect()->route('invoice.processing.invoice.processor')
                ->with('success', __('تم تحديث منتجات الفاتورة ومعلومات الدفع بنجاح'));

        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * تحديث معلومات الدفع للفاتورة
     */
    private function updatePaymentInfo($pos, $request)
    {
        // البحث عن سجل الدفع الموجود أو إنشاء جديد
        $posPayment = PosPayment::where('pos_id', $pos->id)->first();

        if (!$posPayment) {
            $posPayment = new PosPayment();
            $posPayment->pos_id = $pos->id;
            $posPayment->created_by = \Auth::user()->id;
        }

        // حساب المبلغ الإجمالي من المنتجات
        $totalAmount = $pos->items()->sum(\DB::raw('price * quantity'));

        // تحديث معلومات الدفع
        $posPayment->date = $request->payment_date;
        $posPayment->payment_type = $request->payment_type;
        $posPayment->discount = $request->payment_discount ?? 0;

        // تحديث المبالغ حسب نوع الدفع
        if ($request->payment_type === 'cash') {
            $posPayment->cash_amount = $request->cash_amount ?? $totalAmount;
            $posPayment->network_amount = 0;
            $posPayment->amount = $posPayment->cash_amount;
            $posPayment->transaction_number = null;
        } elseif ($request->payment_type === 'network') {
            $posPayment->cash_amount = 0;
            $posPayment->network_amount = $request->network_amount ?? $totalAmount;
            $posPayment->amount = $posPayment->network_amount;
            $posPayment->transaction_number = $request->transaction_number;
        } elseif ($request->payment_type === 'split') {
            $posPayment->cash_amount = $request->cash_amount ?? 0;
            $posPayment->network_amount = $request->network_amount ?? 0;
            $posPayment->amount = $posPayment->cash_amount + $posPayment->network_amount;
            $posPayment->transaction_number = $request->transaction_number;
        }

        $posPayment->save();

        return $posPayment;
    }
}
