@extends('layouts.admin')
@section('page-title')
    {{__('معالج فواتير البيع')}}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item"><a href="{{route('invoice.processing.index')}}">{{__('معالجة فواتير المبيعات')}}</a></li>
    <li class="breadcrumb-item">{{__('معالج فواتير البيع')}}</li>
@endsection
@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    <style>
        .filter-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .filter-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px 10px 0 0;
            margin: -1px -1px 0 -1px;
        }
        .filter-body {
            padding: 20px;
        }
        .filter-row {
            margin-bottom: 15px;
        }
        .filter-buttons {
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
            margin-top: 15px;
        }
        .select2-container {
            width: 100% !important;
        }
        .select2-container--bootstrap-5 .select2-selection {
            min-height: calc(1.5em + 0.75rem + 2px);
            border-radius: 0.375rem;
        }
        .btn-filter {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-filter:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
            color: white;
        }
        .btn-reset {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            color: white;
            padding: 10px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
            color: white;
        }
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* تحسين مظهر الشارات الإحصائية */
        .stats-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: flex-end;
            align-items: center;
        }

        .stats-badge {
            font-size: 0.85rem !important;
            padding: 0.4em 0.7em !important;
            border-radius: 20px !important;
            font-weight: 600 !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .stats-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .stats-badge i {
            font-size: 0.9rem;
        }

        /* تحسين التجاوب للشارات */
        @media (max-width: 768px) {
            .stats-badges {
                justify-content: center;
                margin-top: 10px;
            }

            .stats-badge {
                font-size: 0.8rem !important;
                padding: 0.3em 0.6em !important;
            }
        }

        /* ألوان مخصصة للشارات */
        .badge-delivery {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
            color: #000 !important;
        }

        .badge-returned {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
        }

        .badge-cancelled {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
        }

        .badge-completed {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
        }

        .badge-total {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
        }
    </style>
@endpush

@section('content')
    <div id="printableArea">
        <!-- نموذج الفلترة -->
        <div class="row">
            <div class="col-md-12">
                <div class="filter-card">
                    <div class="filter-header">
                        <h6 class="mb-0">
                            <i class="ti ti-filter me-2"></i>
                            {{ __('فلترة البيانات') }}
                        </h6>
                    </div>
                    <div class="filter-body">
                        <form id="filterForm" method="GET" action="{{ route('invoice.processing.invoice.processor') }}">
                            <div class="row filter-row">
                                <!-- فلتر العميل -->
                                <div class="col-md-3">
                                    <label class="form-label">{{ __('العميل') }}</label>
                                    <select name="customer_id" id="customer_id" class="form-select select2">
                                        <option value="">{{ __('جميع العملاء') }}</option>
                                        @foreach($filterData['customers'] as $customer)
                                            <option value="{{ $customer->id }}"
                                                {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                                                {{ $customer->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- فلتر المستودع -->
                                <div class="col-md-3">
                                    <label class="form-label">{{ __('المستودع') }}</label>
                                    <select name="warehouse_id" id="warehouse_id" class="form-select select2">
                                        <option value="">{{ __('جميع المستودعات') }}</option>
                                        @foreach($filterData['warehouses'] as $warehouse)
                                            <option value="{{ $warehouse->id }}"
                                                {{ request('warehouse_id') == $warehouse->id ? 'selected' : '' }}>
                                                {{ $warehouse->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <!-- فلتر منشئ الفاتورة -->
                                <div class="col-md-6">
                                    <label class="form-label">{{ __('منشئ الفاتورة') }}</label>
                                    <select name="created_by" id="created_by" class="form-select select2">
                                        <option value="">{{ __('جميع المنشئين') }}</option>
                                        @foreach($filterData['creators'] as $creator)
                                            <option value="{{ $creator->id }}"
                                                {{ request('created_by') == $creator->id ? 'selected' : '' }}>
                                                {{ $creator->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="row filter-row">
                                <!-- فلتر التاريخ من -->
                                <div class="col-md-3">
                                    <label class="form-label">{{ __('التاريخ من') }}</label>
                                    <input type="date" name="date_from" id="date_from"
                                           class="form-control"
                                           value="{{ request('date_from') }}">
                                </div>

                                <!-- فلتر التاريخ إلى -->
                                <div class="col-md-3">
                                    <label class="form-label">{{ __('التاريخ إلى') }}</label>
                                    <input type="date" name="date_to" id="date_to"
                                           class="form-control"
                                           value="{{ request('date_to') }}">
                                </div>

                                <!-- فلتر طريقة الدفع وحالة الفاتورة (متعدد الاختيار) -->
                                <div class="col-md-6">
                                    <label class="form-label">{{ __('طريقة الدفع / حالة الفاتورة') }}</label>
                                    <select name="payment_methods[]" id="payment_methods"
                                            class="form-select select2" multiple>
                                        @foreach($filterData['payment_methods'] as $key => $method)
                                            <option value="{{ $key }}"
                                                {{ in_array($key, request('payment_methods', [])) ? 'selected' : '' }}>
                                                {{ $method }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="filter-buttons">
                                <button type="submit" class="btn btn-filter me-2">
                                    <i class="ti ti-search me-1"></i>
                                    {{ __('تطبيق الفلتر') }}
                                </button>
                                <button type="button" class="btn btn-reset" onclick="resetFilters()">
                                    <i class="ti ti-refresh me-1"></i>
                                    {{ __('إعادة تعيين') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">{{ __('معالج فواتير البيع') }}</h5>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="stats-badges">
                                    <!-- إجمالي الفواتير -->
                                    <span class="badge badge-total stats-badge">
                                        <i class="ti ti-file-invoice me-1"></i>
                                        {{ __('إجمالي') }}: {{ count($posPayments) }}
                                    </span>

                                    <!-- الطلبات الجاري توصيلها -->
                                    @php
                                        $deliveryInProgress = $posPayments->filter(function($pos) {
                                            return !empty($pos->customer) &&
                                                   $pos->customer->is_delivery &&
                                                   !$pos->is_payment_set;
                                        })->count();
                                    @endphp

                                    @if($deliveryInProgress > 0)
                                        <span class="badge badge-delivery stats-badge">
                                            <i class="ti ti-truck me-1"></i>
                                            {{ __('جاري التوصيل') }}: {{ $deliveryInProgress }}
                                        </span>
                                    @endif

                                    <!-- الفواتير المكتملة -->
                                    @php
                                        $completedInvoices = $posPayments->filter(function($pos) {
                                            return $pos->status_type != 'returned' &&
                                                   $pos->status_type != 'cancelled' &&
                                                   (empty($pos->customer) ||
                                                    !$pos->customer->is_delivery ||
                                                    $pos->is_payment_set);
                                        })->count();
                                    @endphp

                                    <span class="badge badge-completed stats-badge">
                                        <i class="ti ti-check me-1"></i>
                                        {{ __('مكتملة') }}: {{ $completedInvoices }}
                                    </span>

                                    <!-- الفواتير المرتجعة -->
                                    @php
                                        $returnedInvoices = $posPayments->filter(function($pos) {
                                            return $pos->status_type == 'returned';
                                        })->count();
                                    @endphp

                                    @if($returnedInvoices > 0)
                                        <span class="badge badge-returned stats-badge">
                                            <i class="ti ti-arrow-back-up me-1"></i>
                                            {{ __('مرتجع') }}: {{ $returnedInvoices }}
                                        </span>
                                    @endif

                                    <!-- الفواتير الملغية -->
                                    @php
                                        $cancelledInvoices = $posPayments->filter(function($pos) {
                                            return $pos->status_type == 'cancelled';
                                        })->count();
                                    @endphp

                                    @if($cancelledInvoices > 0)
                                        <span class="badge badge-cancelled stats-badge">
                                            <i class="ti ti-x me-1"></i>
                                            {{ __('ملغية') }}: {{ $cancelledInvoices }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                <tr>
                                    <th>{{__('رقم الفاتورة')}}</th>
                                    <th>{{ __('تاريخ') }}</th>
                                    <th>{{ __('عميل') }}</th>
                                    <th>{{ __('مستودع') }}</th>
                                    <th>{{ __('المجموع الفرعي') }}</th>
                                    <th>{{ __('الخصم') }}</th>
                                    <th>{{ __('المجموع') }}</th>
                                    <th>{{ __('طريقة الدفع') }}</th>
                                    <th>{{ __('منشئ الفاتورة') }}</th>
                                    <th>{{ __('المستخدم') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                                </thead>

                                <tbody>
                                @forelse ($posPayments as $pos)
                                    <tr>
                                        <td class="Id">
                                            <a href="{{ route('invoice.processing.show', $pos->id) }}" class="btn btn-outline-primary">
                                                {{ AUth::user()->posNumberFormat($pos->id) }}
                                            </a>
                                        </td>

                                        <td>{{ Auth::user()->dateFormat($pos->pos_date) }}</td>
                                        <td>{{ !empty($pos->customer) ? $pos->customer->name : __('Walk-in Customer') }}</td>
                                        <td>{{ !empty($pos->warehouse) ? $pos->warehouse->name : '' }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getSubTotal()) }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getTotalDiscount()) }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getTotal()) }}</td>
                                        <td>
                                            @if($pos->status_type == 'returned')
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('مرتجع بضاعة') }} ↩️</span>
                                            @elseif($pos->status_type == 'cancelled')
                                                <span class="badge bg-secondary text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('ملغية') }} ❌</span>
                                            @elseif(!empty($pos->customer) && $pos->customer->is_delivery)
                                                @if($pos->is_payment_set)
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('تم التحصيل من مندوب التوصيل') }} ✅</span>
                                                @else
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('جاري التحصيل') }} 🚚</span>
                                                @endif
                                            @elseif(isset($pos->posPayment) && $pos->posPayment->payment_type)
                                                @if($pos->posPayment->payment_type == 'cash')
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('نقد') }} 💵</span>
                                                @elseif($pos->posPayment->payment_type == 'network')
                                                    <span class="badge bg-info text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('شبكة') }} 💳</span>
                                                @elseif($pos->posPayment->payment_type == 'split')
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('دفع مقسم') }} 💳 💵</span>
                                                @else
                                                    <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('غير مدفوع') }}</span>
                                                @endif
                                            @else
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('غير مدفوع') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if(!empty($pos->createdBy))
                                                @if($pos->createdBy->type == 'company')
                                                    @if(!empty($pos->user_id) && !empty(\App\Models\User::find($pos->user_id)))
                                                        {{ \App\Models\User::find($pos->user_id)->name }}
                                                    @elseif(!empty($pos->shift) && !empty($pos->shift->creator))
                                                        {{ $pos->shift->creator->name }}
                                                    @else
                                                        {{ $pos->createdBy->name }}
                                                    @endif
                                                @else
                                                    {{ $pos->createdBy->name }}
                                                @endif
                                            @else
                                                {{ __('غير معروف') }}
                                            @endif
                                        </td>
                                        <td>
                                            @if(!empty($pos->user))
                                                {{ $pos->user->name }}
                                            @elseif(!empty($pos->user_id) && !empty(\App\Models\User::find($pos->user_id)))
                                                {{ \App\Models\User::find($pos->user_id)->name }}
                                            @elseif(!empty($pos->shift) && !empty($pos->shift->creator))
                                                {{ $pos->shift->creator->name }}
                                            @else
                                                {{ __('غير معروف') }}
                                            @endif
                                        </td>
                                        <td class="Action">
                                            <div class="action-btn bg-primary ms-2">
                                                <a href="{{ route('invoice.processing.edit.products', $pos->id) }}" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="{{ __('تعديل المنتجات') }}">
                                                    <i class="ti ti-edit text-white"></i>
                                                </a>
                                            </div>
                                            <div class="action-btn bg-danger ms-2">
                                                <a href="{{ route('invoice.processing.return', $pos->id) }}" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="{{ __('مرتجع') }}" onclick="return confirm('{{ __('هل أنت متأكد من تغيير حالة الفاتورة إلى مرتجع بضاعة؟') }}')">
                                                    <i class="ti ti-arrow-back-up text-white"></i>
                                                </a>
                                            </div>
                                            <div class="action-btn bg-warning ms-2">
                                                <a href="{{ route('invoice.processing.cancel', $pos->id) }}" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="{{ __('كنسل') }}" onclick="return confirm('{{ __('هل أنت متأكد من تغيير حالة الفاتورة إلى ملغية؟') }}')">
                                                    <i class="ti ti-x text-white"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="11" class="text-center">{{ __('لا توجد فواتير متاحة') }}</td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<!-- jQuery (إذا لم يكن موجوداً) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // تهيئة Select2 للقوائم المنسدلة
    initializeSelect2();

    // تطبيق الفلتر تلقائياً عند تغيير أي حقل
    setupAutoFilter();

    // تحسين تجربة المستخدم
    setupUserExperience();
});

/**
 * تهيئة Select2 لجميع القوائم المنسدلة
 */
function initializeSelect2() {
    // تهيئة Select2 للقوائم المفردة
    $('.select2:not([multiple])').select2({
        theme: 'bootstrap-5',
        placeholder: function() {
            return $(this).find('option:first').text();
        },
        allowClear: true,
        width: '100%',
        language: {
            noResults: function() {
                return '<div class="text-center p-2">{{ __("لا توجد نتائج") }}</div>';
            },
            searching: function() {
                return '<div class="text-center p-2"><i class="fas fa-spinner fa-spin me-2"></i>{{ __("جاري البحث...") }}</div>';
            }
        }
    });

    // تهيئة Select2 للقوائم متعددة الاختيار
    $('#payment_methods').select2({
        theme: 'bootstrap-5',
        placeholder: '{{ __("اختر طرق الدفع / حالة الفاتورة...") }}',
        allowClear: true,
        width: '100%',
        closeOnSelect: false,
        language: {
            noResults: function() {
                return '<div class="text-center p-2">{{ __("لا توجد نتائج") }}</div>';
            },
            searching: function() {
                return '<div class="text-center p-2"><i class="fas fa-spinner fa-spin me-2"></i>{{ __("جاري البحث...") }}</div>';
            }
        },
        templateResult: function(option) {
            if (!option.id) {
                return option.text;
            }

            // إضافة checkbox للخيارات
            var $option = $(
                '<div class="d-flex align-items-center">' +
                    '<input type="checkbox" class="me-2" ' +
                    (option.selected ? 'checked' : '') + '>' +
                    '<span>' + option.text + '</span>' +
                '</div>'
            );

            return $option;
        }
    });
}

/**
 * إعداد الفلترة التلقائية
 */
function setupAutoFilter() {
    // تطبيق الفلتر عند تغيير القوائم المنسدلة
    $('#customer_id, #warehouse_id, #created_by').on('change', function() {
        applyFilters();
    });

    // تطبيق الفلتر عند تغيير طرق الدفع
    $('#payment_methods').on('change', function() {
        applyFilters();
    });

    // تطبيق الفلتر عند تغيير التواريخ
    $('#date_from, #date_to').on('change', function() {
        validateDateRange();
        applyFilters();
    });
}

/**
 * تحسين تجربة المستخدم
 */
function setupUserExperience() {
    // إضافة تأثيرات بصرية عند التحميل
    $('.filter-card').hide().fadeIn(800);

    // تحسين عرض النتائج
    updateResultsCount();

    // إضافة tooltips للأزرار
    $('[data-bs-toggle="tooltip"]').tooltip();

    // تحسين عرض الفلاتر النشطة
    showActiveFilters();
}

/**
 * تطبيق الفلاتر
 */
function applyFilters() {
    // إظهار مؤشر التحميل
    showLoadingIndicator();

    // تأخير قصير لتحسين تجربة المستخدم
    setTimeout(function() {
        $('#filterForm').submit();
    }, 300);
}

/**
 * إعادة تعيين الفلاتر
 */
function resetFilters() {
    // إعادة تعيين جميع الحقول
    $('#filterForm')[0].reset();

    // إعادة تعيين Select2
    $('.select2').val(null).trigger('change');

    // إعادة توجيه إلى الصفحة بدون فلاتر
    window.location.href = '{{ route("invoice.processing.invoice.processor") }}';
}

/**
 * التحقق من صحة نطاق التواريخ
 */
function validateDateRange() {
    var dateFrom = $('#date_from').val();
    var dateTo = $('#date_to').val();

    if (dateFrom && dateTo && dateFrom > dateTo) {
        // تبديل التواريخ إذا كان التاريخ من أكبر من التاريخ إلى
        $('#date_from').val(dateTo);
        $('#date_to').val(dateFrom);

        // إظهار تنبيه
        showAlert('تم تصحيح نطاق التواريخ تلقائياً', 'info');
    }
}

/**
 * إظهار مؤشر التحميل
 */
function showLoadingIndicator() {
    // إضافة overlay للتحميل
    if ($('#loadingOverlay').length === 0) {
        $('body').append(`
            <div id="loadingOverlay" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 9999;
                display: flex;
                align-items: center;
                justify-content: center;
            ">
                <div style="
                    background: white;
                    padding: 20px;
                    border-radius: 10px;
                    text-align: center;
                ">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">{{ __("جاري التحميل...") }}</span>
                    </div>
                    <div class="mt-2">{{ __("جاري تطبيق الفلاتر...") }}</div>
                </div>
            </div>
        `);
    }
}

/**
 * تحديث عدد النتائج والإحصائيات
 */
function updateResultsCount() {
    var totalRows = $('table tbody tr').length;
    var visibleRows = $('table tbody tr:visible').length;

    // تحديث إجمالي الفواتير
    $('.badge-total').html('<i class="ti ti-file-invoice me-1"></i>{{ __("إجمالي") }}: ' + visibleRows);

    // حساب الإحصائيات للصفوف المرئية
    var deliveryInProgress = 0;
    var completedInvoices = 0;
    var returnedInvoices = 0;
    var cancelledInvoices = 0;

    $('table tbody tr:visible').each(function() {
        var paymentCell = $(this).find('td').eq(7); // عمود طريقة الدفع
        var paymentText = paymentCell.text().trim();

        if (paymentText.includes('جاري التحصيل') || paymentText.includes('🚚')) {
            deliveryInProgress++;
        } else if (paymentText.includes('مرتجع بضاعة') || paymentText.includes('↩️')) {
            returnedInvoices++;
        } else if (paymentText.includes('ملغية') || paymentText.includes('❌')) {
            cancelledInvoices++;
        } else {
            completedInvoices++;
        }
    });

    // تحديث شارة التوصيل
    if (deliveryInProgress > 0) {
        if ($('.badge-delivery').length === 0) {
            $('.badge-total').after('<span class="badge badge-delivery stats-badge"><i class="ti ti-truck me-1"></i>{{ __("جاري التوصيل") }}: ' + deliveryInProgress + '</span>');
        } else {
            $('.badge-delivery').html('<i class="ti ti-truck me-1"></i>{{ __("جاري التوصيل") }}: ' + deliveryInProgress);
        }
    } else {
        $('.badge-delivery').remove();
    }

    // تحديث شارة المكتملة
    $('.badge-completed').html('<i class="ti ti-check me-1"></i>{{ __("مكتملة") }}: ' + completedInvoices);

    // تحديث شارة المرتجعة
    if (returnedInvoices > 0) {
        if ($('.badge-returned').length === 0) {
            $('.badge-completed').after('<span class="badge badge-returned stats-badge"><i class="ti ti-arrow-back-up me-1"></i>{{ __("مرتجع") }}: ' + returnedInvoices + '</span>');
        } else {
            $('.badge-returned').html('<i class="ti ti-arrow-back-up me-1"></i>{{ __("مرتجع") }}: ' + returnedInvoices);
        }
    } else {
        $('.badge-returned').remove();
    }

    // تحديث شارة الملغية
    if (cancelledInvoices > 0) {
        if ($('.badge-cancelled').length === 0) {
            $('.stats-badges').append('<span class="badge badge-cancelled stats-badge"><i class="ti ti-x me-1"></i>{{ __("ملغية") }}: ' + cancelledInvoices + '</span>');
        } else {
            $('.badge-cancelled').html('<i class="ti ti-x me-1"></i>{{ __("ملغية") }}: ' + cancelledInvoices);
        }
    } else {
        $('.badge-cancelled').remove();
    }
}

/**
 * إظهار الفلاتر النشطة
 */
function showActiveFilters() {
    var activeFilters = [];

    // فحص الفلاتر النشطة
    if ($('#customer_id').val()) {
        activeFilters.push('{{ __("العميل") }}: ' + $('#customer_id option:selected').text());
    }

    if ($('#warehouse_id').val()) {
        activeFilters.push('{{ __("المستودع") }}: ' + $('#warehouse_id option:selected').text());
    }

    if ($('#created_by').val()) {
        activeFilters.push('{{ __("منشئ الفاتورة") }}: ' + $('#created_by option:selected').text());
    }

    if ($('#date_from').val()) {
        activeFilters.push('{{ __("من تاريخ") }}: ' + $('#date_from').val());
    }

    if ($('#date_to').val()) {
        activeFilters.push('{{ __("إلى تاريخ") }}: ' + $('#date_to').val());
    }

    var selectedPaymentMethods = $('#payment_methods').val();
    if (selectedPaymentMethods && selectedPaymentMethods.length > 0) {
        var methodNames = [];
        selectedPaymentMethods.forEach(function(method) {
            methodNames.push($('#payment_methods option[value="' + method + '"]').text());
        });
        activeFilters.push('{{ __("طرق الدفع / حالة الفاتورة") }}: ' + methodNames.join(', '));
    }

    // عرض الفلاتر النشطة
    if (activeFilters.length > 0) {
        var activeFiltersHtml = '<div class="alert alert-info mt-2">' +
            '<strong>{{ __("الفلاتر النشطة") }}:</strong><br>' +
            activeFilters.join('<br>') +
            '</div>';

        $('.filter-body').append(activeFiltersHtml);
    }
}

/**
 * إظهار تنبيه
 */
function showAlert(message, type = 'success') {
    var alertClass = 'alert-' + type;
    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('.filter-card').before(alertHtml);

    // إخفاء التنبيه تلقائياً بعد 3 ثوان
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 3000);
}

// تحسينات إضافية للأداء
$(window).on('load', function() {
    // إخفاء مؤشر التحميل إذا كان موجوداً
    $('#loadingOverlay').fadeOut();

    // تحديث الإحصائيات عند تحميل الصفحة
    updateResultsCount();

    // تحسين عرض الجدول
    if ($.fn.DataTable) {
        var table = $('.datatable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
            },
            "responsive": true,
            "pageLength": 25,
            "order": [[ 0, "desc" ]],
            "drawCallback": function(settings) {
                // تحديث الإحصائيات عند تغيير الصفحة أو البحث
                updateResultsCount();
            }
        });

        // تحديث الإحصائيات عند البحث في الجدول
        table.on('search.dt', function() {
            updateResultsCount();
        });
    }

    // إضافة تأثيرات تفاعلية للشارات
    $('.stats-badge').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );
});
</script>
@endpush
